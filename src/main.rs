

struct Solution;
use std::collections::{HashMap, HashSet};
// use std::collections::HashMap;
// Definition for a binary tree node.
#[derive(Debug, PartialEq, Eq)]
pub struct TreeNode {
  pub val: i32,
  pub left: Option<Rc<RefCell<TreeNode>>>,
  pub right: Option<Rc<RefCell<TreeNode>>>,
}

impl TreeNode {
  #[inline]
  pub fn new(val: i32) -> Self {
    TreeNode {
      val,
      left: None,
      right: None
    }
  }
}
use std::i32;
use std::rc::Rc;
use std::cell::RefCell;
// Definition for a binary tree node.
// #[derive(Debug, PartialEq, Eq)]
// pub struct TreeNode {
//   pub val: i32,
//   pub left: Option<Rc<RefCell<TreeNode>>>,
//   pub right: Option<Rc<RefCell<TreeNode>>>,
// }
//
// impl TreeNode {
//   #[inline]
//   pub fn new(val: i32) -> Self {
//     TreeNode {
//       val,
//       left: None,
//       right: None
//     }
//   }
// }
// use std::rc::Rc;
// use std::cell::RefCell;
// use std::collections::HashMap;
// use std::collections::HashSet;
impl Solution {
    pub fn pacific_atlantic(heights: Vec<Vec<i32>>) -> Vec<Vec<i32>> {
        let mut maxh = Vec::new();
        let mut maxv = Vec::new();
        let mut r = Vec::new();
        for i in 0..heights.len() {
            let m = heights[i].iter().max();
            maxh.push(m);
        }
        let mut hh = Vec::new();
        // for i in 0..heights.len() {
            
            for j in 0..heights[0].len() {
                let mut jj = Vec::new();
                for i in 0..heights.len() {
                    jj.push(heights[i][j]);
                }
            }
        // }
        for i in 0..jj.len() {
            let m = jj[i].iter().max();
            maxv.push(m);
        }
        for i in 0..heights.len() {
            for j in 0..heights[0].len() {
                let a = maxh[i];
                let b = maxv[j];
                let c = heights[i][j];
                if c == a || c == b {
                    r.push(vec![i,j]);
                }
            }
        }
        return r;
    }
}



fn main() {
    let a = Solution::assign_bikes(vec![vec![0,0],vec![2,1]], vec![vec![1,2],vec![3,3]]);
    println!("{:?}", a);
}
